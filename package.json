{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "seed": "node scripts/seed-database.js", "check-db": "node scripts/check-database.js"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@types/jsonwebtoken": "^9.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "geist": "^1.3.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.475.0", "mongodb": "^6.8.0", "next": "^15.3.2", "next-themes": "^0.3.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "next-pwa": "^5.6.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}