if(!self.define){let e,s={};const a=(a,n)=>(a=new URL(a+".js",n).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(n,c)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(s[i])return;let t={};const f=e=>a(e,i),r={module:{uri:i},exports:t,require:f};s[i]=Promise.all(n.map(e=>r[e]||f(e))).then(e=>(c(...e),t))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"29621062a1d91f8626b5402b84fefd07"},{url:"/_next/static/AnvhIjLAOkUHirB6U8o8w/_buildManifest.js",revision:"f7278b5ac2c102883fabe09f599e21fc"},{url:"/_next/static/AnvhIjLAOkUHirB6U8o8w/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/328-c840ece3e31a6910.js",revision:"c840ece3e31a6910"},{url:"/_next/static/chunks/341.e70881eb548cce62.js",revision:"e70881eb548cce62"},{url:"/_next/static/chunks/352-979f66b0f12d1d8b.js",revision:"979f66b0f12d1d8b"},{url:"/_next/static/chunks/452-68e07a06c4e06edc.js",revision:"68e07a06c4e06edc"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4bd1b696-9911af18dede28aa.js",revision:"9911af18dede28aa"},{url:"/_next/static/chunks/502-d3e4f8f651eb2047.js",revision:"d3e4f8f651eb2047"},{url:"/_next/static/chunks/534-8f5fe9541424122d.js",revision:"8f5fe9541424122d"},{url:"/_next/static/chunks/619-0aed530b8cc60c75.js",revision:"0aed530b8cc60c75"},{url:"/_next/static/chunks/620-6d1f21b9f65343d2.js",revision:"6d1f21b9f65343d2"},{url:"/_next/static/chunks/63-93f76d3a2601f3bd.js",revision:"93f76d3a2601f3bd"},{url:"/_next/static/chunks/697-8211cad82e9b67cd.js",revision:"8211cad82e9b67cd"},{url:"/_next/static/chunks/756-d7102109a5c928e8.js",revision:"d7102109a5c928e8"},{url:"/_next/static/chunks/874-3e820bd666038662.js",revision:"3e820bd666038662"},{url:"/_next/static/chunks/947-f2c1489a91b5f58c.js",revision:"f2c1489a91b5f58c"},{url:"/_next/static/chunks/964-42be1c1b65719424.js",revision:"42be1c1b65719424"},{url:"/_next/static/chunks/app/(site)/experience/page-f30f2e5ff1aac981.js",revision:"f30f2e5ff1aac981"},{url:"/_next/static/chunks/app/(site)/layout-70cc207dd52574bf.js",revision:"70cc207dd52574bf"},{url:"/_next/static/chunks/app/(site)/page-0b96da9ff3ca48d2.js",revision:"0b96da9ff3ca48d2"},{url:"/_next/static/chunks/app/_not-found/page-32c3bbbc9e6d47be.js",revision:"32c3bbbc9e6d47be"},{url:"/_next/static/chunks/app/admin/about/page-b4ac7c4abae11ec4.js",revision:"b4ac7c4abae11ec4"},{url:"/_next/static/chunks/app/admin/contact-settings/page-23a946bb126dbb47.js",revision:"23a946bb126dbb47"},{url:"/_next/static/chunks/app/admin/cv-management/page-dcd8e5bafea04f19.js",revision:"dcd8e5bafea04f19"},{url:"/_next/static/chunks/app/admin/dashboard/page-f30f2e5ff1aac981.js",revision:"f30f2e5ff1aac981"},{url:"/_next/static/chunks/app/admin/experience/edit/%5Bid%5D/page-1ff417fafdf218e8.js",revision:"1ff417fafdf218e8"},{url:"/_next/static/chunks/app/admin/experience/new/page-c504915f3a025bbc.js",revision:"c504915f3a025bbc"},{url:"/_next/static/chunks/app/admin/experience/page-35de24736d051426.js",revision:"35de24736d051426"},{url:"/_next/static/chunks/app/admin/layout-ad4767c7a4924467.js",revision:"ad4767c7a4924467"},{url:"/_next/static/chunks/app/admin/login/page-0a8b8a877e623a3d.js",revision:"0a8b8a877e623a3d"},{url:"/_next/static/chunks/app/admin/messages/page-fdcf97d0753e2223.js",revision:"fdcf97d0753e2223"},{url:"/_next/static/chunks/app/admin/page-f30f2e5ff1aac981.js",revision:"f30f2e5ff1aac981"},{url:"/_next/static/chunks/app/admin/skills/edit/%5Bid%5D/page-5bda2a9d27aac4ac.js",revision:"5bda2a9d27aac4ac"},{url:"/_next/static/chunks/app/admin/skills/new/page-9c7626a5f81e1551.js",revision:"9c7626a5f81e1551"},{url:"/_next/static/chunks/app/admin/skills/page-b042736ed969faf9.js",revision:"b042736ed969faf9"},{url:"/_next/static/chunks/app/api/auth/login/route-f30f2e5ff1aac981.js",revision:"f30f2e5ff1aac981"},{url:"/_next/static/chunks/app/api/auth/logout/route-f30f2e5ff1aac981.js",revision:"f30f2e5ff1aac981"},{url:"/_next/static/chunks/app/layout-b7655102413c5d08.js",revision:"b7655102413c5d08"},{url:"/_next/static/chunks/b1644e8c-00c2f3d60a9a5bdb.js",revision:"00c2f3d60a9a5bdb"},{url:"/_next/static/chunks/framework-e4f935a0d9f5a98d.js",revision:"e4f935a0d9f5a98d"},{url:"/_next/static/chunks/main-45487cd8857fe06b.js",revision:"45487cd8857fe06b"},{url:"/_next/static/chunks/main-app-d3730eef8276924b.js",revision:"d3730eef8276924b"},{url:"/_next/static/chunks/pages/_app-b915069545280a98.js",revision:"b915069545280a98"},{url:"/_next/static/chunks/pages/_error-d90c0ca91b817ff7.js",revision:"d90c0ca91b817ff7"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-f54010baacff9594.js",revision:"f54010baacff9594"},{url:"/_next/static/css/09b6a0116f8cfd8d.css",revision:"09b6a0116f8cfd8d"},{url:"/_next/static/media/028c0d39d2e8f589-s.p.woff2",revision:"c47061a6ce9601b5dea8da0c9e847f79"},{url:"/_next/static/media/5b01f339abf2f1a5.p.woff2",revision:"c36289c8eb40b089247060459534962c"},{url:"/android-chrome-192x192.png",revision:"b8e76b480079cf5a30dba61a52f6a1a1"},{url:"/android-chrome-512x512.png",revision:"e1637c2a8f32d08ac6b965e8de7d0da0"},{url:"/apple-touch-icon.png",revision:"a1ecd594402ffac7334a5f9d2e9b2049"},{url:"/favicon-16x16.png",revision:"71500608c8ec1f8640ead5ed043c6fa0"},{url:"/favicon-32x32.png",revision:"15ddc0450dfbf2ba27acbc78f0f63cf2"},{url:"/manifest.json",revision:"dbb055271fbaa4541650c3ce7a60e183"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:n})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
