'use server';

import { revalidatePath } from 'next/cache';
import { cvRepository } from '../database/repositories/cv.repository';
import { isBuildContext } from '../utils/env';
import type { CvInfo } from '@/types';

export async function getCurrentCvInfo(): Promise<CvInfo | null> {
  if (isBuildContext()) {
    return null;
  }

  const data = await cvRepository.getCurrentCv();
  if (!data) return null;

  // Transform the data to match the expected CvInfo type
  return {
    fileName: data.fileName,
    lastModified: data.lastModified,
  } as CvInfo;
}

export async function uploadCv(formData: FormData) {
  if (isBuildContext()) {
    return { success: false, message: 'Cannot access database during build time' };
  }

  const file = formData.get('cv') as File;
  if (!file) {
    return { success: false, message: 'No file provided' };
  }

  try {
    // In a real implementation, you would save the file to storage
    // and create a record in the database
    const cvData = {
      fileName: file.name,
      fileSize: file.size,
      uploadedAt: new Date().toISOString(),
      fileUrl: `/uploads/cv/${file.name}`, // This would be the actual file path
    };

    const result = await cvRepository.create(cvData as any);
    
    if (result) {
      // Clean up old CVs, keep only the latest one
      await cvRepository.deleteOldCvs(1);
      
      revalidatePath('/admin/cv');
      revalidatePath('/');
      return { success: true, message: 'CV uploaded successfully', data: result };
    }

    return { success: false, message: 'Failed to upload CV' };
  } catch (error) {
    console.error('Error uploading CV:', error);
    return { success: false, message: 'Failed to upload CV' };
  }
}

export async function deleteCv(id: string) {
  if (isBuildContext()) {
    return { success: false, message: 'Cannot access database during build time' };
  }

  try {
    const result = await cvRepository.delete(id);
    
    if (result) {
      revalidatePath('/admin/cv');
      revalidatePath('/');
      return { success: true, message: 'CV deleted successfully' };
    }

    return { success: false, message: 'Failed to delete CV or CV not found' };
  } catch (error) {
    console.error('Error deleting CV:', error);
    return { success: false, message: 'Failed to delete CV' };
  }
}
